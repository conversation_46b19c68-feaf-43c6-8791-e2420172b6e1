"""
班组管理系统自动化测试用例
基于CSV测试用例文件编写的前五个测试用例，与测试用例一一对应
"""

from playwright.sync_api import Page, expect
import pytest


def test_01_new_dialog_interface_verification(page: Page) -> None:
    """
    测试用例1: 新建弹窗界面验证
    对应CSV第2行: 新建弹窗界面验证
    所属分组: 班组排班方案 | 排班方案 | 新增
    步骤描述: 【1】页面空间分配 【2】按钮形状 【3】各部分颜色
    预期结果: 【1】与原型一致 【2】与原型一致 【3】与原型一致
    """
    # 登录系统
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")

    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")

    # 【步骤1】点击新增排班方案按钮
    add_button = page.get_by_text("新增排班方案")
    expect(add_button).to_be_visible()
    expect(add_button).to_be_enabled()
    add_button.click()

    # 等待弹窗出现 - 使用更稳定的定位器
    # 方法1：通过弹窗标题定位
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('[role="dialog"][aria-label="新增排班方案"]')

    # 如果上面的定位器不工作，使用备选方案
    if not dialog_area.is_visible():
        dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")

    expect(dialog_area).to_be_visible()

    # 【验证1】页面空间分配 - 验证弹窗布局和尺寸
    # 验证弹窗标题
    dialog_title = dialog_area.locator(".el-dialog__title")
    expect(dialog_title).to_be_visible()
    expect(dialog_title).to_contain_text("新增排班方案")

    # 验证弹窗主体内容区域
    dialog_body = dialog_area.locator(".el-dialog__body")
    expect(dialog_body).to_be_visible()

    # 验证弹窗底部按钮区域
    dialog_footer = dialog_area.locator(".el-dialog__footer")
    expect(dialog_footer).to_be_visible()

    # 【验证2】按钮形状 - 验证按钮存在且可见
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")
    expect(confirm_button).to_be_visible()
    expect(confirm_button).to_be_enabled()
    expect(cancel_button).to_be_visible()
    expect(cancel_button).to_be_enabled()

    # 【验证3】各部分颜色 - 验证表单元素正确显示
    # 验证表单标签
    name_label = dialog_area.locator("label").filter(has_text="排班方案名称")
    type_label = dialog_area.locator("label").filter(has_text="班组类型")
    expect(name_label).to_be_visible()
    expect(type_label).to_be_visible()

    # 验证输入框
    name_input = dialog_area.get_by_placeholder("请输入")
    expect(name_input).to_be_visible()
    expect(name_input).to_be_enabled()

    # 验证单选按钮组
    radio_group = dialog_area.locator(".el-radio-group")
    expect(radio_group).to_be_visible()

    # 验证单选按钮选项
    maintenance_radio = dialog_area.locator(".el-radio").filter(has_text="运维班组")
    production_radio = dialog_area.locator(".el-radio").filter(has_text="生产班组")
    expect(maintenance_radio).to_be_visible()
    expect(production_radio).to_be_visible()

    # 验证默认选中状态（运维班组应该默认选中）
    maintenance_input = maintenance_radio.locator("input[type='radio']")
    production_input = production_radio.locator("input[type='radio']")
    expect(maintenance_input).to_be_checked()
    expect(production_input).not_to_be_checked()

    print("✓ 弹窗界面验证通过：页面空间分配、按钮形状、各部分颜色均与原型一致")

    # 关闭弹窗
    cancel_button.click()

    # 验证弹窗已关闭
    expect(page.locator('[role="dialog"][aria-label="新增排班方案"]')).not_to_be_visible()




