"""
班组管理系统自动化测试用例
基于Playwright框架编写的前五个核心测试用例
"""

from playwright.sync_api import Page, expect
import pytest


def test_login_and_navigate_to_team_management(page: Page) -> None:
    """
    测试用例1: 登录系统并导航到班组管理页面
    验证用户能够成功登录并访问班组管理功能
    """
    # 访问登录页面
    page.goto("http://10.12.140.38:8090/#/login")
    
    # 输入用户名
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("zpl")
    
    # 输入密码
    page.get_by_placeholder("请输入密码").click()
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    
    # 点击登录按钮
    page.get_by_role("button", name="登录").click()
    
    # 等待页面加载并导航到班组管理
    page.wait_for_load_state("networkidle")
    
    # 点击班组管理菜单
    page.locator("div").filter(has_text="班组管理").nth(4).click()
    
    # 验证页面标题或关键元素
    expect(page.locator(".page")).to_be_visible()


def test_create_new_scheduling_plan(page: Page) -> None:
    """
    测试用例2: 新增排班方案
    验证用户能够成功创建新的排班方案
    """
    # 前置条件：已登录并在班组管理页面
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")
    
    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")
    
    # 点击新增排班方案按钮
    page.get_by_role("button", name="新增排班方案").click()
    
    # 等待弹窗出现
    page.wait_for_selector(".CetDialog", state="visible")
    
    # 输入排班方案名称
    page.get_by_label("排班方案名称").fill("自动化测试排班方案")
    
    # 选择班组类型（运维班组）
    page.get_by_label("运维班组").click()
    
    # 点击保存按钮
    page.get_by_role("button", name="保存").click()
    
    # 验证保存成功
    page.wait_for_load_state("networkidle")
    expect(page.locator("text=自动化测试排班方案")).to_be_visible()


def test_search_scheduling_plan(page: Page) -> None:
    """
    测试用例3: 搜索排班方案
    验证搜索功能能够正确筛选排班方案
    """
    # 前置条件：已登录并在排班方案页面
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")
    
    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")
    
    # 在搜索框中输入搜索关键词
    search_input = page.get_by_placeholder("请输入内容")
    search_input.click()
    search_input.fill("测试")
    
    # 触发搜索（通过回车或点击搜索图标）
    search_input.press("Enter")
    
    # 等待搜索结果加载
    page.wait_for_load_state("networkidle")
    
    # 验证搜索结果包含关键词
    page.wait_for_timeout(1000)  # 等待结果更新
    
    # 验证表格中显示的结果包含搜索关键词
    table_rows = page.locator(".piemTableBox tbody tr")
    if table_rows.count() > 0:
        # 如果有结果，验证包含搜索关键词
        expect(page.locator("text=测试")).to_be_visible()
    else:
        # 如果没有结果，验证显示暂无数据
        expect(page.locator("text=暂无数据").or_(page.locator("text=列表暂无数据"))).to_be_visible()


def test_filter_by_team_type(page: Page) -> None:
    """
    测试用例4: 按班组类型筛选
    验证班组类型筛选功能正常工作
    """
    # 前置条件：已登录并在排班方案页面
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")
    
    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")
    
    # 点击班组类型筛选框
    team_type_selector = page.locator(".customElSelect").filter(has_text="班组类型")
    team_type_selector.click()
    
    # 选择运维班组
    page.get_by_text("运维班组").click()
    
    # 等待筛选结果加载
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(1000)
    
    # 验证筛选结果
    table_rows = page.locator(".piemTableBox tbody tr")
    if table_rows.count() > 0:
        # 验证表格中显示的都是运维班组
        for i in range(table_rows.count()):
            row = table_rows.nth(i)
            expect(row.locator("td").nth(2)).to_contain_text("运维班组")
    
    # 切换到生产班组筛选
    team_type_selector.click()
    page.get_by_text("生产班组").click()
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(1000)
    
    # 验证生产班组筛选结果
    table_rows = page.locator(".piemTableBox tbody tr")
    if table_rows.count() > 0:
        for i in range(table_rows.count()):
            row = table_rows.nth(i)
            expect(row.locator("td").nth(2)).to_contain_text("生产班组")


def test_switch_to_shift_plan_tab(page: Page) -> None:
    """
    测试用例5: 切换到班次方案标签页
    验证能够正确切换到班次方案标签页并查看内容
    """
    # 前置条件：已登录并在排班方案页面
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")
    
    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")
    
    # 首先选择一个排班方案（如果列表不为空）
    table_rows = page.locator(".piemTableBox tbody tr")
    if table_rows.count() > 0:
        # 点击第一行选择排班方案
        table_rows.first().click()
        page.wait_for_timeout(500)
    
    # 点击班次方案标签页
    page.get_by_role("tab", name="班次方案").click()
    
    # 等待标签页内容加载
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(1000)
    
    # 验证班次方案标签页已激活
    expect(page.locator(".el-tabs__item.is-active")).to_contain_text("班次方案")
    
    # 验证班次方案内容区域可见
    expect(page.locator(".shiftPlan")).to_be_visible()
    
    # 验证新增班次方案按钮存在
    expect(page.get_by_role("button", name="新增班次方案")).to_be_visible()
    
    # 切换回班组列表标签页验证切换功能
    page.get_by_role("tab", name="班组列表").click()
    page.wait_for_timeout(500)
    
    # 验证班组列表标签页已激活
    expect(page.locator(".el-tabs__item.is-active")).to_contain_text("班组列表")
    
    # 验证班组列表内容区域可见
    expect(page.locator(".teamList")).to_be_visible()


if __name__ == "__main__":
    # 运行测试的示例代码
    print("班组管理系统测试用例已生成")
    print("包含以下5个测试用例：")
    print("1. test_login_and_navigate_to_team_management - 登录并导航到班组管理")
    print("2. test_create_new_scheduling_plan - 新增排班方案")
    print("3. test_search_scheduling_plan - 搜索排班方案")
    print("4. test_filter_by_team_type - 按班组类型筛选")
    print("5. test_switch_to_shift_plan_tab - 切换班次方案标签页")
